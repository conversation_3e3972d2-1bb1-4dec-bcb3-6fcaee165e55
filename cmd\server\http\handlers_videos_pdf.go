package http

import (
	"net/http"
	"strconv"
	"ziaacademy-backend/internal/models"

	"github.com/gin-gonic/gin"
)

// GetContent godoc
//
//	@Summary		Get Content
//	@Description	get videos and studymaterial for given chapter
//	     @Security       BearerAuth
//
// @Param			chapter_id	query		uint	true	"Chapter ID"
//
//	@Tags			questions
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	models.Content
//	@Failure		400	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/content [get]
//
// GetQuestions is the HTTP handler to get list of questions for
// given topic and difficulty level
// GetContent is the HTTP handler to get content for a chapter
// This handler does not use any framework, instead just the standard library
func (h *Handlers) GetContent(ctx *gin.Context) {
	chStr := ctx.Query("chapter_id")
	if chStr == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing chapter_id parameter"})
		return
	}
	chapterID, _ := strconv.Atoi(chStr)
	content, err := h.db.GetContent(ctx.Request.Context(), uint(chapterID))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, gin.H{"content": content})
}

// AddVideo godoc
//
//		@Summary	    AddVideo
//		@Description	add a new video
//	     @Security       BearerAuth
//	 @Param			item	body	models.VideoForCreate	true	"video details"
//		@Tags			library
//		@Accept			json
//		@Produce		json
//		@Success		200	{object}	models.SimpleEntityResponse
//		@Failure		400	{object}	HTTPError
//		@Failure		404	{object}	HTTPError
//		@Failure		500	{object}	HTTPError
//		@Router			/videos [post]
func (h *Handlers) AddVideo(ctx *gin.Context) {
	videoInput := new(models.VideoForCreate)
	if err := ctx.ShouldBindJSON(videoInput); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	video := &models.Video{
		Name:        videoInput.Name,
		DisplayName: videoInput.DisplayName,
		VideoUrl:    videoInput.VideoUrl,
		ViewCount:   0, // Default to 0
	}

	createdVideo, err := h.db.AddVideo(ctx.Request.Context(), video, videoInput.ChapterName)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Return only ID and name
	response := models.SimpleEntityResponse{
		ID:   createdVideo.ID,
		Name: createdVideo.Name,
	}
	ctx.JSON(http.StatusOK, response)
}

// AddStudyMaterial godoc
//
//		@Summary	    AddStudyMaterial
//		@Description	add a new material
//	     @Security       BearerAuth
//	 @Param			item	body	models.MaterialForCreate	true	"material details"
//		@Tags			library
//		@Accept			json
//		@Produce		json
//		@Success		200	{object}	models.SimpleEntityResponse
//		@Failure		400	{object}	HTTPError
//		@Failure		404	{object}	HTTPError
//		@Failure		500	{object}	HTTPError
//		@Router			/studymaterials [post]
//
// AddStudyMaterial is the HTTP handler to add a new pdf to a chapter
func (h *Handlers) AddStudyMaterial(ctx *gin.Context) {
	materialInput := new(models.MaterialForCreate)
	if err := ctx.ShouldBindJSON(materialInput); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	material := &models.StudyMaterial{
		Name:        materialInput.Name,
		DisplayName: materialInput.DisplayName,
		Url:         materialInput.Url,
	}

	createdMaterial, err := h.db.AddStudyMaterial(ctx.Request.Context(), material, materialInput.ChapterName)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Return only ID and name
	response := models.SimpleEntityResponse{
		ID:   createdMaterial.ID,
		Name: createdMaterial.Name,
	}
	ctx.JSON(http.StatusOK, response)
}
