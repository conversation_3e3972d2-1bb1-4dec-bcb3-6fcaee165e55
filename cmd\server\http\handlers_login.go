package http

import (
	"log/slog"
	"net/http"
	"time"
	"ziaacademy-backend/internal/models"
	"ziaacademy-backend/internal/token"

	"github.com/gin-gonic/gin"
)

// Login godoc
//
//		@Summary		Login
//		@Description	login with email and password
//	 @Param			item	body	models.Credentials	true	"user_email and password"
//		@Tags			login
//		@Accept			json
//		@Produce		json
//		@Success		200	{object}	string
//		@Failure		400	{object}	HTTPError
//		@Failure		404	{object}	HTTPError
//		@Failure		500	{object}	HTTPError
//		@Router			/login [post]
//
// Login is the HTTP handler to login to the application
func (h *Handlers) Login(ctx *gin.Context) {
	start := time.Now()
	clientIP := ctx.ClientIP()

	creds := new(models.Credentials)
	if err := ctx.ShouldBindJSON(creds); err != nil {
		duration := time.Since(start)
		slog.Warn("Login failed - invalid request body",
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	slog.Info("Login attempt",
		"email", creds.UserEmail,
		"client_ip", clientIP,
	)

	// Check if the username exists and password matches
	userID, err := h.db.ValidateUserPassword(ctx, creds.UserEmail, creds.Password)
	if err != nil {
		duration := time.Since(start)
		slog.Warn("Login failed - invalid credentials",
			"email", creds.UserEmail,
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
		return
	}

	// Generate JWT token
	token, err := token.GenerateJWT(userID)
	if err != nil {
		duration := time.Since(start)
		slog.Error("Login failed - token generation error",
			"email", creds.UserEmail,
			"user_id", userID,
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
		return
	}

	student, err := h.db.GetStudentByUserID(ctx, userID)
	if err != nil {
		duration := time.Since(start)
		slog.Error("Login failed - student retrieval error",
			"email", creds.UserEmail,
			"user_id", userID,
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve student details"})
		return
	}

	duration := time.Since(start)
	slog.Info("Login successful",
		"email", creds.UserEmail,
		"user_id", userID,
		"client_ip", clientIP,
		"duration_ms", duration.Milliseconds(),
	)

	// Return the token to the user
	ctx.JSON(http.StatusOK, gin.H{"token": token, "student": student})
}
