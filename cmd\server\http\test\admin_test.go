package test

import (
	"encoding/json"
	"net/http"
	"testing"
	"ziaacademy-backend/internal/models"

	"github.com/stretchr/testify/assert"
)

func TestCreateAdminStandalone(t *testing.T) {
	// Skip if noAuthRouter is not initialized
	if noAuthRouter == nil {
		t.Skip("NoAuthRouter not initialized - skipping admin test")
		return
	}

	// Clean up before test
	if db != nil {
		db.Exec("DELETE FROM users WHERE email = ?", "<EMAIL>")
	}

	adminPayload := models.AdminForCreate{
		FullName:       "Test Admin",
		Email:          "<EMAIL>",
		PhoneNumber:    "5555555555",
		ContactAddress: "789 Admin Avenue",
		Password:       "adminpass123",
	}

	resp := requestExecutionHelper(http.MethodPost, "/api/admins", adminPayload)

	assert.Equal(t, http.StatusCreated, resp.Code)

	// Parse response
	var response map[string]interface{}
	err := json.Unmarshal(resp.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.NotNil(t, response["token"])
	assert.NotNil(t, response["admin"])

	// Verify admin details in response
	adminData := response["admin"].(map[string]interface{})
	assert.Equal(t, "Test Admin", adminData["FullName"])
	assert.Equal(t, "<EMAIL>", adminData["Email"])
	assert.Equal(t, "5555555555", adminData["PhoneNumber"])
	assert.Equal(t, "Admin", adminData["Role"])

	// Verify admin was created in database if db is available
	if db != nil {
		var savedAdmin models.User
		result := db.First(&savedAdmin, "email = ?", "<EMAIL>")
		assert.Nil(t, result.Error)
		assert.Equal(t, "Test Admin", savedAdmin.FullName)
		assert.Equal(t, "<EMAIL>", savedAdmin.Email)
		assert.Equal(t, "5555555555", savedAdmin.PhoneNumber)
		assert.Equal(t, "Admin", savedAdmin.Role)
		assert.NotEmpty(t, savedAdmin.PasswordHash) // Password should be hashed

		// Cleanup
		db.Delete(&savedAdmin)
	}
}
