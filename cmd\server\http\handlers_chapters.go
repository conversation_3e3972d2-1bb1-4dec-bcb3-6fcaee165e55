package http

import (
	"net/http"
	"strconv"
	"ziaacademy-backend/internal/models"

	"github.com/gin-gonic/gin"
)

// GetChapters godoc
//
//			@Summary		Get Chapters
//			@Description	get chapters for a subject_id
//	     @Security       BearerAuth
//		 @Param			subject_id	query		int	true	"Subject ID"
//			@Tags			chapters
//			@Accept			json
//			@Produce		json
//			@Success		200	{object}	[]models.Chapter
//			@Failure		400	{object}	HTTPError
//			@Failure		404	{object}	HTTPError
//			@Failure		500	{object}	HTTPError
//			@Router			/chapters [get]
func (h *Handlers) GetChapters(ctx *gin.Context) {
	subStr := ctx.Query("subject_id")
	if subStr == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing subject_id parameter"})
		return
	}
	subjectID, _ := strconv.Atoi(subStr)
	chapters, err := h.db.GetChapters(ctx.Request.Context(), uint(subjectID))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, chapters)
}

// CreateChapters godoc
//
//			@Summary		CreateChapters
//			@Description	create new chapter
//			@Description
//			@Description	Field Constraints:
//			@Description	- name: Chapter name must be unique (required)
//			@Description	- displayName: Display name for the chapter (required)
//			@Description	- subjectName: Must reference an existing subject (required)
//	     @Security       BearerAuth
//		 @Param			item	body	models.ChapterForCreate	true	"chapter details"
//			@Tags			chapters
//			@Accept			json
//			@Produce		json
//			@Success		200	{object}	models.Chapter
//			@Failure		400	{object}	HTTPError
//			@Failure		404	{object}	HTTPError
//			@Failure		500	{object}	HTTPError
//			@Router			/chapters [post]
//
// CreateChapter is the HTTP handler to create a new chapter
func (h *Handlers) CreateChapter(ctx *gin.Context) {
	chapterInput := new(models.ChapterForCreate)
	if err := ctx.ShouldBindJSON(chapterInput); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	chapter := &models.Chapter{
		Name:        chapterInput.Name,
		DisplayName: chapterInput.DisplayName,
	}

	createdChapter, err := h.db.CreateChapter(ctx.Request.Context(), chapter, chapterInput.SubjectName)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, createdChapter)
}
