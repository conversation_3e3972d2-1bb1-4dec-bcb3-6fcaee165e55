package test

import (
	"encoding/json"
	"net/http"
	"testing"
	"ziaacademy-backend/internal/models"

	"github.com/stretchr/testify/assert"
)

func TestCreateCourseWithNewFields(t *testing.T) {
	// Clean up before test
	db.Exec("DELETE FROM courses WHERE name IN (?, ?)", "IIT-JEE Physics Course", "NEET Biology Course")

	// Test creating IIT-JEE course (paid)
	iitjeeCourse := models.CourseForCreate{
		Name:           "IIT-JEE Physics Course",
		Description:    "Comprehensive IIT-JEE Physics preparation course",
		Price:          2999,
		Discount:       15.0,
		DurationInDays: 365,
		IsFree:         false,
		CourseType:     models.CourseTypeIITJEE,
		Subjects:       []models.SubjectForCreate{},
	}

	// Create IIT-JEE course
	resp := requestExecutionHelper(http.MethodPost, "/api/courses", iitjeeCourse)
	assert.Equal(t, http.StatusOK, resp.Code)

	var createdIITJEECourse models.SimpleEntityResponse
	err := json.Unmarshal(resp.Body.Bytes(), &createdIITJEECourse)
	assert.Nil(t, err)
	assert.Equal(t, "IIT-JEE Physics Course", createdIITJEECourse.Name)
	assert.NotZero(t, createdIITJEECourse.ID)

	// Verify the full course was created correctly in the database
	var fullCourse models.Course
	err = db.First(&fullCourse, createdIITJEECourse.ID).Error
	assert.Nil(t, err)
	assert.Equal(t, false, fullCourse.IsFree)
	assert.Equal(t, models.CourseTypeIITJEE, fullCourse.CourseType)
	assert.Equal(t, 2999, fullCourse.Price)

	// Test creating NEET course (free)
	neetCourse := models.CourseForCreate{
		Name:           "NEET Biology Course",
		Description:    "Free NEET Biology preparation course",
		Price:          0,
		Discount:       0.0,
		DurationInDays: 180,
		IsFree:         true,
		CourseType:     models.CourseTypeNEET,
		Subjects:       []models.SubjectForCreate{},
	}

	// Create NEET course
	resp2 := requestExecutionHelper(http.MethodPost, "/api/courses", neetCourse)
	assert.Equal(t, http.StatusOK, resp2.Code)

	var createdNEETCourse models.SimpleEntityResponse
	err = json.Unmarshal(resp2.Body.Bytes(), &createdNEETCourse)
	assert.Nil(t, err)
	assert.Equal(t, "NEET Biology Course", createdNEETCourse.Name)
	assert.NotZero(t, createdNEETCourse.ID)

	// Verify the full course was created correctly in the database
	var fullNEETCourse models.Course
	err = db.First(&fullNEETCourse, createdNEETCourse.ID).Error
	assert.Nil(t, err)
	assert.Equal(t, true, fullNEETCourse.IsFree)
	assert.Equal(t, models.CourseTypeNEET, fullNEETCourse.CourseType)
	assert.Equal(t, 0, fullNEETCourse.Price)

	// Clean up after test
	db.Delete(&fullCourse)
	db.Delete(&fullNEETCourse)
}

func TestCreateCourseWithInvalidCourseType(t *testing.T) {
	// Test creating course with invalid course type
	invalidCourse := models.CourseForCreate{
		Name:           "Invalid Course",
		Description:    "Course with invalid type",
		Price:          1000,
		Discount:       10.0,
		DurationInDays: 30,
		IsFree:         false,
		CourseType:     "INVALID-TYPE",
		Subjects:       []models.SubjectForCreate{},
	}

	resp := requestExecutionHelper(http.MethodPost, "/api/courses", invalidCourse)
	assert.Equal(t, http.StatusBadRequest, resp.Code)

	var errorResponse map[string]interface{}
	err := json.Unmarshal(resp.Body.Bytes(), &errorResponse)
	assert.Nil(t, err)
	assert.Contains(t, errorResponse["error"].(string), "Invalid course type")
}

func TestGetCoursesOrganizedByCategory(t *testing.T) {
	// Clean up before test - be more thorough to avoid interference from other tests
	db.Exec("DELETE FROM students_courses")
	db.Exec("DELETE FROM courses_tests")
	db.Exec("DELETE FROM courses_subjects")
	db.Exec("DELETE FROM courses")
	db.Exec("DELETE FROM students")
	db.Exec("DELETE FROM users WHERE email IN (?, ?)", "<EMAIL>", "<EMAIL>")

	// Create test courses with different combinations
	courses := []models.Course{
		{
			Name:           "Free IIT-JEE Course",
			Description:    "Free IIT-JEE course",
			Price:          0,
			Discount:       0.0,
			DurationInDays: 30,
			IsFree:         true,
			CourseType:     models.CourseTypeIITJEE,
		},
		{
			Name:           "Paid IIT-JEE Course",
			Description:    "Paid IIT-JEE course",
			Price:          1999,
			Discount:       10.0,
			DurationInDays: 60,
			IsFree:         false,
			CourseType:     models.CourseTypeIITJEE,
		},
		{
			Name:           "Free NEET Course",
			Description:    "Free NEET course",
			Price:          0,
			Discount:       0.0,
			DurationInDays: 45,
			IsFree:         true,
			CourseType:     models.CourseTypeNEET,
		},
		{
			Name:           "Paid NEET Course",
			Description:    "Paid NEET course",
			Price:          2499,
			Discount:       20.0,
			DurationInDays: 90,
			IsFree:         false,
			CourseType:     models.CourseTypeNEET,
		},
		{
			Name:           "Unenrolled Paid Course",
			Description:    "Paid course student is not enrolled in",
			Price:          3999,
			Discount:       5.0,
			DurationInDays: 120,
			IsFree:         false,
			CourseType:     models.CourseTypeIITJEE,
		},
	}

	// Create all courses
	var createdCourses []models.Course
	for _, course := range courses {
		resp := requestExecutionHelper(http.MethodPost, "/api/courses", course)
		assert.Equal(t, http.StatusOK, resp.Code)

		var createdCourseResponse models.SimpleEntityResponse
		err := json.Unmarshal(resp.Body.Bytes(), &createdCourseResponse)
		assert.Nil(t, err)

		// Fetch the full course from database
		var fullCourse models.Course
		err = db.First(&fullCourse, createdCourseResponse.ID).Error
		assert.Nil(t, err)
		createdCourses = append(createdCourses, fullCourse)
	}

	// Create a student to test the GetCourses API
	studentPayload := models.StudentForCreate{
		UserForCreate: models.UserForCreate{
			FullName:       "Test Student",
			Email:          "<EMAIL>",
			PhoneNumber:    "9876543210",
			ContactAddress: "Test Address",
		},
		ParentPhone: "1234567890",
		ParentEmail: "<EMAIL>",
	}

	studentResp := requestExecutionHelper(http.MethodPost, "/api/students", studentPayload)
	assert.Equal(t, http.StatusOK, studentResp.Code)

	var studentResponse models.CreatedStudentResponse
	err := json.Unmarshal(studentResp.Body.Bytes(), &studentResponse)
	assert.Nil(t, err)
	token := studentResponse.Token
	assert.NotEmpty(t, token)

	// Enroll student in some paid courses (but not all)
	// Find the paid courses to enroll in
	var paidIITJEECourse, paidNEETCourse models.Course
	for _, course := range createdCourses {
		if course.Name == "Paid IIT-JEE Course" {
			paidIITJEECourse = course
		} else if course.Name == "Paid NEET Course" {
			paidNEETCourse = course
		}
	}

	// Enroll student in paid courses (simulate enrollment)
	db.Exec("INSERT INTO students_courses (student_id, course_id) VALUES (?, ?)",
		studentResponse.Student.ID, paidIITJEECourse.ID)
	db.Exec("INSERT INTO students_courses (student_id, course_id) VALUES (?, ?)",
		studentResponse.Student.ID, paidNEETCourse.ID)

	// Get courses using the new organized structure
	resp := authenticatedRequestHelper(http.MethodGet, "/api/courses", nil, token)
	assert.Equal(t, http.StatusOK, resp.Code)

	var coursesByCategory models.CoursesByCategory
	err = json.Unmarshal(resp.Body.Bytes(), &coursesByCategory)
	assert.Nil(t, err)

	// Verify structure - should have free courses and only enrolled paid courses
	assert.NotEmpty(t, coursesByCategory.FreeCourses)
	assert.NotEmpty(t, coursesByCategory.PaidCourses)

	// Count total courses returned
	totalFreeCourses := 0
	totalPaidCourses := 0

	// Verify free courses are organized by course type
	freeCoursesFound := make(map[string]bool)
	for _, coursesByType := range coursesByCategory.FreeCourses {
		assert.True(t, coursesByType.CourseType == models.CourseTypeIITJEE || coursesByType.CourseType == models.CourseTypeNEET)
		freeCoursesFound[coursesByType.CourseType] = true
		totalFreeCourses += len(coursesByType.Courses)

		// All courses in this group should be free and of the same type
		for _, course := range coursesByType.Courses {
			assert.True(t, course.IsFree)
			assert.Equal(t, coursesByType.CourseType, course.CourseType)
		}
	}

	// Verify paid courses are organized by course type
	paidCoursesFound := make(map[string]bool)
	for _, coursesByType := range coursesByCategory.PaidCourses {
		assert.True(t, coursesByType.CourseType == models.CourseTypeIITJEE || coursesByType.CourseType == models.CourseTypeNEET)
		paidCoursesFound[coursesByType.CourseType] = true
		totalPaidCourses += len(coursesByType.Courses)

		// All courses in this group should be paid, enrolled, and of the same type
		for _, course := range coursesByType.Courses {
			assert.False(t, course.IsFree)
			assert.True(t, course.Purchased) // Should be enrolled
			assert.Equal(t, coursesByType.CourseType, course.CourseType)
		}
	}

	// Should have found both IIT-JEE and NEET in free categories
	assert.True(t, freeCoursesFound[models.CourseTypeIITJEE])
	assert.True(t, freeCoursesFound[models.CourseTypeNEET])

	// Should have found both IIT-JEE and NEET in paid categories (enrolled courses)
	assert.True(t, paidCoursesFound[models.CourseTypeIITJEE])
	assert.True(t, paidCoursesFound[models.CourseTypeNEET])

	// Verify counts: should have 2 free courses and 2 enrolled paid courses
	// (The "Unenrolled Paid Course" should NOT be returned)
	assert.Equal(t, 2, totalFreeCourses, "Should return 2 free courses")
	assert.Equal(t, 2, totalPaidCourses, "Should return 2 enrolled paid courses")

	// Test 2: Test admin user behavior - should return ALL courses
	t.Run("AdminUserGetsCourses", func(t *testing.T) {
		// Create an admin user
		adminPayload := models.AdminForCreate{
			FullName:       "Test Admin",
			Email:          "<EMAIL>",
			PhoneNumber:    "5555555555",
			ContactAddress: "789 Admin Avenue",
			Password:       "adminpass123",
		}

		adminResp := requestExecutionHelper(http.MethodPost, "/api/admins", adminPayload)
		assert.Equal(t, http.StatusCreated, adminResp.Code)

		var adminResponse map[string]interface{}
		err := json.Unmarshal(adminResp.Body.Bytes(), &adminResponse)
		assert.Nil(t, err)
		adminToken := adminResponse["token"].(string)
		assert.NotEmpty(t, adminToken)

		// Get courses as admin
		adminCoursesResp := authenticatedRequestHelper(http.MethodGet, "/api/courses", nil, adminToken)
		assert.Equal(t, http.StatusOK, adminCoursesResp.Code)

		var adminCoursesByCategory models.CoursesByCategory
		err = json.Unmarshal(adminCoursesResp.Body.Bytes(), &adminCoursesByCategory)
		assert.Nil(t, err)

		// Count total courses returned for admin
		totalAdminFreeCourses := 0
		totalAdminPaidCourses := 0

		for _, coursesByType := range adminCoursesByCategory.FreeCourses {
			totalAdminFreeCourses += len(coursesByType.Courses)
		}
		for _, coursesByType := range adminCoursesByCategory.PaidCourses {
			totalAdminPaidCourses += len(coursesByType.Courses)
			// For admin, all paid courses should have Purchased = false
			for _, course := range coursesByType.Courses {
				assert.False(t, course.Purchased, "Admin should see Purchased=false for all courses")
			}
		}

		// Admin should see ALL courses: 2 free + 3 paid = 5 total
		assert.Equal(t, 2, totalAdminFreeCourses, "Admin should see 2 free courses")
		assert.Equal(t, 3, totalAdminPaidCourses, "Admin should see ALL 3 paid courses (including unenrolled)")

		// Clean up admin user
		adminData := adminResponse["admin"].(map[string]interface{})
		adminID := uint(adminData["id"].(float64))
		db.Exec("DELETE FROM users WHERE id = ?", adminID)
	})

	// Clean up
	for _, course := range createdCourses {
		db.Delete(&course)
	}
	// Fetch the full student for cleanup since we only have ID and name
	var fullStudent models.Student
	db.Preload("User").First(&fullStudent, studentResponse.Student.ID)
	db.Delete(&fullStudent.User)
	db.Delete(&fullStudent)
}

func TestGetCoursesFilteringBehavior(t *testing.T) {
	// Clean up before test - be more thorough
	db.Exec("DELETE FROM students_courses")
	db.Exec("DELETE FROM courses_tests")
	db.Exec("DELETE FROM courses_subjects")
	db.Exec("DELETE FROM courses")
	db.Exec("DELETE FROM students")
	db.Exec("DELETE FROM users WHERE email IN (?, ?)", "<EMAIL>", "<EMAIL>")

	// Create test courses
	freeCourse := models.Course{
		Name:           "Test Free Course",
		Description:    "A free course for testing",
		Price:          0,
		Discount:       0.0,
		DurationInDays: 30,
		IsFree:         true,
		CourseType:     models.CourseTypeIITJEE,
	}

	enrolledPaidCourse := models.Course{
		Name:           "Test Enrolled Paid Course",
		Description:    "A paid course student is enrolled in",
		Price:          1999,
		Discount:       10.0,
		DurationInDays: 60,
		IsFree:         false,
		CourseType:     models.CourseTypeIITJEE,
	}

	unenrolledPaidCourse := models.Course{
		Name:           "Test Unenrolled Paid Course",
		Description:    "A paid course student is NOT enrolled in",
		Price:          2999,
		Discount:       15.0,
		DurationInDays: 90,
		IsFree:         false,
		CourseType:     models.CourseTypeNEET,
	}

	// Create courses
	var createdCourses []models.Course
	for _, course := range []models.Course{freeCourse, enrolledPaidCourse, unenrolledPaidCourse} {
		resp := requestExecutionHelper(http.MethodPost, "/api/courses", course)
		assert.Equal(t, http.StatusOK, resp.Code)

		var createdCourseResponse models.SimpleEntityResponse
		err := json.Unmarshal(resp.Body.Bytes(), &createdCourseResponse)
		assert.Nil(t, err)

		// Fetch the full course from database
		var fullCourse models.Course
		err = db.First(&fullCourse, createdCourseResponse.ID).Error
		assert.Nil(t, err)
		createdCourses = append(createdCourses, fullCourse)
	}

	// Create a student
	studentPayload := models.StudentForCreate{
		UserForCreate: models.UserForCreate{
			FullName:       "Filter Test Student",
			Email:          "<EMAIL>",
			PhoneNumber:    "9876543211",
			ContactAddress: "Test Address",
		},
		ParentPhone: "1234567891",
		ParentEmail: "<EMAIL>",
	}

	studentResp := requestExecutionHelper(http.MethodPost, "/api/students", studentPayload)
	assert.Equal(t, http.StatusOK, studentResp.Code)

	var studentResponse models.CreatedStudentResponse
	err := json.Unmarshal(studentResp.Body.Bytes(), &studentResponse)
	assert.Nil(t, err)
	token := studentResponse.Token

	// Enroll student only in the "enrolled paid course"
	var enrolledCourseID uint
	for _, course := range createdCourses {
		if course.Name == "Test Enrolled Paid Course" {
			enrolledCourseID = course.ID
			break
		}
	}
	db.Exec("INSERT INTO students_courses (student_id, course_id) VALUES (?, ?)",
		studentResponse.Student.ID, enrolledCourseID)

	// Get courses
	resp := authenticatedRequestHelper(http.MethodGet, "/api/courses", nil, token)
	assert.Equal(t, http.StatusOK, resp.Code)

	var coursesByCategory models.CoursesByCategory
	err = json.Unmarshal(resp.Body.Bytes(), &coursesByCategory)
	assert.Nil(t, err)

	// Collect all returned course names
	var returnedCourseNames []string
	for _, coursesByType := range coursesByCategory.FreeCourses {
		for _, course := range coursesByType.Courses {
			returnedCourseNames = append(returnedCourseNames, course.Name)
		}
	}
	for _, coursesByType := range coursesByCategory.PaidCourses {
		for _, course := range coursesByType.Courses {
			returnedCourseNames = append(returnedCourseNames, course.Name)
		}
	}

	// Verify filtering behavior
	assert.Contains(t, returnedCourseNames, "Test Free Course", "Free course should be returned")
	assert.Contains(t, returnedCourseNames, "Test Enrolled Paid Course", "Enrolled paid course should be returned")
	assert.NotContains(t, returnedCourseNames, "Test Unenrolled Paid Course", "Unenrolled paid course should NOT be returned")

	// Verify total count
	assert.Equal(t, 2, len(returnedCourseNames), "Should return exactly 2 courses")

	// Test admin behavior - should return ALL courses
	t.Run("AdminGetsAllCourses", func(t *testing.T) {
		// Create an admin user
		adminPayload := models.AdminForCreate{
			FullName:       "Filter Admin",
			Email:          "<EMAIL>",
			PhoneNumber:    "4444444444",
			ContactAddress: "456 Admin Street",
			Password:       "adminpass456",
		}

		adminResp := requestExecutionHelper(http.MethodPost, "/api/admins", adminPayload)
		assert.Equal(t, http.StatusCreated, adminResp.Code)

		var adminResponse map[string]interface{}
		err := json.Unmarshal(adminResp.Body.Bytes(), &adminResponse)
		assert.Nil(t, err)
		adminToken := adminResponse["token"].(string)

		// Get courses as admin
		adminCoursesResp := authenticatedRequestHelper(http.MethodGet, "/api/courses", nil, adminToken)
		assert.Equal(t, http.StatusOK, adminCoursesResp.Code)

		var adminCoursesByCategory models.CoursesByCategory
		err = json.Unmarshal(adminCoursesResp.Body.Bytes(), &adminCoursesByCategory)
		assert.Nil(t, err)

		// Collect all returned course names for admin
		var adminReturnedCourseNames []string
		for _, coursesByType := range adminCoursesByCategory.FreeCourses {
			for _, course := range coursesByType.Courses {
				adminReturnedCourseNames = append(adminReturnedCourseNames, course.Name)
			}
		}
		for _, coursesByType := range adminCoursesByCategory.PaidCourses {
			for _, course := range coursesByType.Courses {
				adminReturnedCourseNames = append(adminReturnedCourseNames, course.Name)
				// Admin should see Purchased=false for all courses
				assert.False(t, course.Purchased, "Admin should see Purchased=false")
			}
		}

		// Admin should see ALL 3 courses
		assert.Contains(t, adminReturnedCourseNames, "Test Free Course", "Admin should see free course")
		assert.Contains(t, adminReturnedCourseNames, "Test Enrolled Paid Course", "Admin should see enrolled paid course")
		assert.Contains(t, adminReturnedCourseNames, "Test Unenrolled Paid Course", "Admin should see unenrolled paid course")
		assert.Equal(t, 3, len(adminReturnedCourseNames), "Admin should see all 3 courses")

		// Clean up admin user
		adminData := adminResponse["admin"].(map[string]interface{})
		adminID := uint(adminData["id"].(float64))
		db.Exec("DELETE FROM users WHERE id = ?", adminID)
	})

	// Clean up
	for _, course := range createdCourses {
		db.Delete(&course)
	}
	// Fetch the full student for cleanup since we only have ID and name
	var fullStudent models.Student
	db.Preload("User").First(&fullStudent, studentResponse.Student.ID)
	db.Delete(&fullStudent.User)
	db.Delete(&fullStudent)
}
