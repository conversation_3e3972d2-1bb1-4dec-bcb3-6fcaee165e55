-- Migration to add is_free and course_type fields to courses table
-- Run this script to update existing database schema

-- Add is_free column
ALTER TABLE courses ADD COLUMN IF NOT EXISTS is_free BOOLEAN NOT NULL DEFAULT FALSE;

-- Add course_type column
ALTER TABLE courses ADD COLUMN IF NOT EXISTS course_type VARCHAR(10) NOT NULL DEFAULT 'IIT-JEE' CHECK (course_type IN ('IIT-JEE', 'NEET'));

-- Update existing courses to have valid course_type values
-- This is a safe default - you may want to update specific courses manually
UPDATE courses SET course_type = 'IIT-JEE' WHERE course_type IS NULL OR course_type = '';

-- Verify the changes
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'courses' 
AND column_name IN ('is_free', 'course_type');
