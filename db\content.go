package db

import (
	"context"
	"fmt"
	"log/slog"
	"time"
	"ziaacademy-backend/internal/models"
)

func (p *DbPlugin) GetContent(ctx context.Context, chapter_id uint) (*models.Content, error) {
	start := time.Now()
	slog.Debug("Retrieving content for chapter", "chapter_id", chapter_id)

	var chapter models.Chapter
	// Retrieve the chapter with given ID, along with its videos and study materials
	if err := p.db.Preload("Videos").Preload("StudyMaterials").
		First(&chapter, chapter_id).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve chapter content",
			"chapter_id", chapter_id,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	content := &models.Content{
		Videos: chapter.Videos,
		Pdfs:   chapter.StudyMaterials,
	}

	duration := time.Since(start)
	slog.Info("Content retrieved successfully",
		"chapter_id", chapter_id,
		"chapter_name", chapter.Name,
		"video_count", len(chapter.Videos),
		"study_material_count", len(chapter.StudyMaterials),
		"duration_ms", duration.Milliseconds(),
	)
	return content, nil
}

func (p *DbPlugin) AddVideo(ctx context.Context, video *models.Video, chapterName string) (*models.Video, error) {
	start := time.Now()
	slog.Info("Adding video to chapter",
		"video_name", video.Name,
		"video_display_name", video.DisplayName,
		"chapter_name", chapterName,
	)

	// Find the chapter by name
	var chapter models.Chapter
	if err := p.db.Where("name = ?", chapterName).First(&chapter).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Chapter not found for video addition",
			"video_name", video.Name,
			"chapter_name", chapterName,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("chapter '%s' not found: %w", chapterName, err)
	}

	// Set the chapter ID
	video.ChapterID = chapter.ID

	slog.Debug("Creating video with chapter association",
		"video_name", video.Name,
		"chapter_id", chapter.ID,
		"chapter_name", chapterName,
	)

	res := p.db.Create(video)
	duration := time.Since(start)

	if res.Error != nil {
		slog.Error("Failed to create video",
			"video_name", video.Name,
			"chapter_name", chapterName,
			"chapter_id", chapter.ID,
			"error", res.Error.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, res.Error
	}

	slog.Info("Video added successfully",
		"video_id", video.ID,
		"video_name", video.Name,
		"video_display_name", video.DisplayName,
		"chapter_name", chapterName,
		"chapter_id", chapter.ID,
		"duration_ms", duration.Milliseconds(),
	)
	return video, nil
}

func (p *DbPlugin) AddStudyMaterial(ctx context.Context,
	pdf *models.StudyMaterial, chapterName string) (*models.StudyMaterial, error) {
	start := time.Now()
	slog.Info("Adding study material to chapter",
		"material_name", pdf.Name,
		"material_display_name", pdf.DisplayName,
		"chapter_name", chapterName,
	)

	// Find the chapter by name
	var chapter models.Chapter
	if err := p.db.Where("name = ?", chapterName).First(&chapter).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Chapter not found for study material addition",
			"material_name", pdf.Name,
			"chapter_name", chapterName,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("chapter '%s' not found: %w", chapterName, err)
	}

	// Set the chapter ID
	pdf.ChapterID = chapter.ID

	slog.Debug("Creating study material with chapter association",
		"material_name", pdf.Name,
		"chapter_id", chapter.ID,
		"chapter_name", chapterName,
	)

	res := p.db.Create(pdf)
	duration := time.Since(start)

	if res.Error != nil {
		slog.Error("Failed to create study material",
			"material_name", pdf.Name,
			"chapter_name", chapterName,
			"chapter_id", chapter.ID,
			"error", res.Error.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, res.Error
	}

	slog.Info("Study material added successfully",
		"material_id", pdf.ID,
		"material_name", pdf.Name,
		"material_display_name", pdf.DisplayName,
		"chapter_name", chapterName,
		"chapter_id", chapter.ID,
		"duration_ms", duration.Milliseconds(),
	)
	return pdf, nil
}
