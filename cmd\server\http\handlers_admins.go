package http

import (
	"log/slog"
	"net/http"
	"ziaacademy-backend/internal/models"
	"ziaacademy-backend/internal/token"

	"github.com/gin-gonic/gin"
)

// CreateAdmin godoc
//
//	@Summary		Create Admin User
//	@Description	Create a new admin user with role "Admin"
//	@Description
//	@Description	Field Constraints:
//	@Description	- fullName: Required field
//	@Description	- email: Must be valid email format and unique across all users (required)
//	@Description	- phoneNumber: Must be unique across all users (required)
//	@Description	- password: Must be at least 6 characters long (required)
//	@Description	- contactAddress: Optional field
//	@Security       BearerAuth
//	@Param			admin	body	models.AdminForCreate	true	"Admin user details"
//	@Tags			admins
//	@Accept			json
//	@Produce		json
//	@Success		201	{object}	models.User
//	@Failure		400	{object}	HTTPError
//	@Failure		401	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/admins [post]
func (h *Handlers) CreateAdmin(ctx *gin.Context) {
	adminInput := new(models.AdminForCreate)
	if err := ctx.ShouldBindJSON(adminInput); err != nil {
		slog.Error("Failed to bind admin input", "error", err.Error())
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	slog.Debug("Processing admin creation request", "email", adminInput.Email)

	// Create admin user model
	admin := models.User{
		FullName:       adminInput.FullName,
		Email:          adminInput.Email,
		PhoneNumber:    adminInput.PhoneNumber,
		ContactAddress: adminInput.ContactAddress,
		Role:           "Admin",
		PasswordHash:   adminInput.Password, // This will be hashed in the database layer
		EmailVerified:  false,
		PhoneVerified:  false,
	}

	createdAdmin, err := h.db.CreateAdmin(ctx.Request.Context(), &admin)
	if err != nil {
		slog.Error("Failed to create admin", "email", adminInput.Email,
			"error", err.Error())
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	slog.Debug("Created admin", "email", createdAdmin.Email)

	// Generate JWT token for the new admin
	token, err := token.GenerateJWT(createdAdmin.ID)
	if err != nil {
		slog.Error("Failed to generate token", "email", adminInput.Email, "error", err.Error())
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
		return
	}

	slog.Debug("Generated token", "email", adminInput.Email)

	// Return the token and created admin details
	ctx.JSON(http.StatusCreated, gin.H{
		"token": token,
		"admin": createdAdmin,
	})
}
