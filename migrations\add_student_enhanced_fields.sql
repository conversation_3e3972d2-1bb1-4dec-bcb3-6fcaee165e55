-- Migration to add enhanced student information fields to students table
-- Run this script to update existing database schema

-- Add institute column
ALTER TABLE students ADD COLUMN IF NOT EXISTS institute VARCHAR(255);

-- Add class column with check constraint (allowing empty strings and NULL)
ALTER TABLE students ADD COLUMN IF NOT EXISTS class VARCHAR(10) CHECK (class IS NULL OR class = '' OR class IN ('9th', '10th', '11th', '12th', 'dropper'));

-- Add stream column with check constraint (allowing empty strings and NULL)
ALTER TABLE students ADD COLUMN IF NOT EXISTS stream VARCHAR(10) CHECK (stream IS NULL OR stream = '' OR stream IN ('IIT-JEE', 'NEET'));

-- Add city_or_town column
ALTER TABLE students ADD COLUMN IF NOT EXISTS city_or_town VARCHAR(255);

-- Add state column
ALTER TABLE students ADD COLUMN IF NOT EXISTS state VARCHAR(255);

-- Verify the changes
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'students' 
AND column_name IN ('institute', 'class', 'stream', 'city_or_town', 'state')
ORDER BY column_name;
